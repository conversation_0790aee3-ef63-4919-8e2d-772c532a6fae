import { auth } from './app/lib/auth';

export default auth((req) => {
  const { pathname } = req.nextUrl;

  // Define public routes that don't require authentication
  const publicRoutes = [
    '/auth/login',
    '/auth/register',
    '/api/auth',
    '/api/auth/register',
  ];

  // Allow all API auth routes
  if (pathname.startsWith('/api/auth')) {
    return;
  }

  // Allow public routes
  if (publicRoutes.some(route => pathname.startsWith(route))) {
    return;
  }

  // Allow static files and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.includes('.')
  ) {
    return;
  }

  // If no session and trying to access protected route, redirect to login
  if (!req.auth) {
    const loginUrl = new URL('/auth/login', req.url);
    loginUrl.searchParams.set('callbackUrl', pathname);
    return Response.redirect(loginUrl);
  }
});

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (authentication API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (images, etc.)
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|.*\\.).*)',
  ],
};
