{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@auth/core": "^0.41.1", "@auth/mongodb-adapter": "^3.11.1", "@aws-sdk/client-s3": "^3.918.0", "@aws-sdk/s3-request-presigner": "^3.918.0", "@fortawesome/fontawesome-svg-core": "^7.1.0", "@fortawesome/free-solid-svg-icons": "^7.1.0", "@fortawesome/react-fontawesome": "^3.1.0", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "formidable": "^3.5.4", "mongoose": "^8.19.2", "multer": "^2.0.2", "next": "^15.5.6", "next-auth": "^5.0.0-beta.29", "react": "19.2.0", "react-dom": "19.2.0", "zod": "^4.1.12"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/formidable": "^3.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "16.0.0", "tailwindcss": "^4", "typescript": "^5"}}