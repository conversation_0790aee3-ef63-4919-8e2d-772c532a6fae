import { handlers } from '../../../lib/auth';
import { NextRequest } from 'next/server';

// Force this API route to use Node.js runtime to support crypto module
export const runtime = 'nodejs';

// Add logging for debugging
export async function GET(request: NextRequest) {
  console.log('NextAuth GET request received');
  return handlers.GET(request);
}

export async function POST(request: NextRequest) {
  console.log('NextAuth POST request received');
  return handlers.POST(request);
}
