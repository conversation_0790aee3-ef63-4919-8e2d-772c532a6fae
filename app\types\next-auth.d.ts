import { DefaultSession, DefaultUser } from 'next-auth';
import { AdapterUser } from '@auth/core/adapters';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      firstName: string;
      lastName: string;
      username: string;
      email: string;
    } & DefaultSession['user'];
  }

  interface User extends DefaultUser {
    firstName: string;
    lastName: string;
    username: string;
    email: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    firstName: string;
    lastName: string;
    username: string;
  }
}
