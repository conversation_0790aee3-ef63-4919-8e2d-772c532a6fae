import NextAuth from 'next-auth';
import Credentials from 'next-auth/providers/credentials';
import { MongoDBAdapter } from '@auth/mongodb-adapter';
import clientPromise from './db';
import { connectToDatabase } from './db';
import User from './models/User';
import type { Adapter } from 'next-auth/adapters';

console.log('Auth config - NODE_ENV:', process.env.NODE_ENV);
console.log('Auth config - NEXTAUTH_URL:', process.env.NEXTAUTH_URL);
console.log('Auth config - NEXTAUTH_SECRET exists:', !!process.env.NEXTAUTH_SECRET);

export const { handlers, signIn, signOut, auth } = NextAuth({
  adapter: MongoDBAdapter(clientPromise) as Adapter,
  providers: [
    Credentials({
      name: 'credentials',
      credentials: {
        login: { label: 'Username or Email', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        console.log('Authorize function called with credentials:', credentials);
        
        if (!credentials?.login || !credentials?.password) {
          console.log('No login or password provided');
          return null;
        }

        try {
          await connectToDatabase();
          console.log('Database connected successfully');

          // Find user by username or email
          const user = await User.findOne({
            $or: [
              { username: (credentials.login as string).toLowerCase() },
              { email: (credentials.login as string).toLowerCase() }
            ]
          });

          console.log('User found:', !!user);

          if (!user) {
            console.log('User not found');
            return null;
          }

          const isPasswordValid = await user.comparePassword(credentials.password as string);
          console.log('Password valid:', isPasswordValid);

          if (!isPasswordValid) {
            console.log('Invalid password');
            return null;
          }

          console.log('User authenticated successfully:', {
            id: user._id.toString(),
            firstName: user.firstName,
            lastName: user.lastName,
            username: user.username,
            email: user.email,
          });

          return {
            id: user._id.toString(),
            firstName: user.firstName,
            lastName: user.lastName,
            username: user.username,
            email: user.email,
          };
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async jwt({ token, user }) {
      console.log('JWT callback - Token before:', token);
      console.log('JWT callback - User:', user);
      console.log('JWT callback - NODE_ENV:', process.env.NODE_ENV);
      console.log('JWT callback - NEXTAUTH_SECRET exists:', !!process.env.NEXTAUTH_SECRET);

      if (user) {
        token.firstName = user.firstName;
        token.lastName = user.lastName;
        token.username = user.username;
        token.id = user.id;
      }

      console.log('JWT callback - Token after:', token);
      return token;
    },
    async session({ session, token }) {
      console.log('Session callback - Session before:', session);
      console.log('Session callback - Token:', token);
      console.log('Session callback - NODE_ENV:', process.env.NODE_ENV);

      if (token) {
        session.user.id = token.id as string;
        session.user.firstName = token.firstName as string;
        session.user.lastName = token.lastName as string;
        session.user.username = token.username as string;
      }

      console.log('Session callback - Session after:', session);
      return session;
    },
  },
  pages: {
    signIn: '/auth/login',
  },
  secret: process.env.NEXTAUTH_SECRET,
  trustHost: true,
});
