@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: #000;
  color: #fff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  width: 100vw;
}

/* Custom Spotify-like styles */
.container {
  display: flex;
  height: 100vh;
  width: 100vw;
  max-width: 100vw;
  overflow: hidden;
}

.sidebar {
  width: 240px;
  background-color: #000;
  padding: 24px 12px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.logo {
  font-size: 28px;
  font-weight: bold;
  padding: 0 12px;
  margin-bottom: 10px;
  color: #1ed760;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  color: #b3b3b3;
  text-decoration: none;
  border-radius: 4px;
  transition: color 0.2s;
  cursor: pointer;
}

.nav-item:hover {
  color: #fff;
}

.nav-item.active {
  color: #fff;
}

.nav-item svg {
  width: 24px;
  height: 24px;
}

.divider {
  height: 1px;
  background-color: #282828;
  margin: 8px 0;
}

.playlist-item {
  padding: 8px 12px;
  color: #b3b3b3;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.2s;
}

.playlist-item:hover {
  color: #fff;
}

.main-content {
  flex: 1;
  background-color: #121212;
  overflow-y: auto;
  padding-bottom: 100px;
  width: 100%;
  min-width: 0;
}

.top-bar {
  background-color: #121212;
  padding: 16px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.nav-buttons {
  display: flex;
  gap: 16px;
}

.nav-btn {
  background-color: rgba(0, 0, 0, 0.7);
  border: none;
  color: #fff;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.nav-btn:hover {
  background-color: rgba(0, 0, 0, 0.9);
}

.user-profile {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 4px 16px 4px 4px;
  border-radius: 23px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-profile:hover {
  background-color: rgba(0, 0, 0, 0.9);
}

.avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

/* SearchBar Component Styles */
.search-bar-container {
  flex: 1;
  max-width: 420px;
  margin: 0;
  position: relative;
}

.search-form {
  width: 100%;
}

.search-input-wrapper {
  background-color: #242424;
  border-radius: 500px;
  padding: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  width: 100%;
  height: 48px;
  border: 1px solid transparent;
  position: relative;
}

.search-input-wrapper:hover {
  background-color: #2a2a2a;
  border-color: #535353;
}

.search-input-wrapper.focused {
  background-color: #2a2a2a;
  border-color: #ffffff;
}

.search-icon {
  color: #a7a7a7;
  flex-shrink: 0;
  margin-left: 16px;
  width: 16px;
  height: 16px;
}

.search-input {
  background: transparent;
  border: none;
  outline: none;
  color: #ffffff;
  font-size: 14px;
  font-weight: 400;
  flex: 1;
  padding: 0 8px;
  font-family: inherit;
}

.search-input::placeholder {
  color: #a7a7a7;
  font-weight: 400;
}

.clear-button {
  background: none;
  border: none;
  color: #a7a7a7;
  cursor: pointer;
  padding: 8px;
  margin-right: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.clear-button:hover {
  color: #ffffff;
}

.content-section {
  padding: 16px 32px;
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 16px;
}

.greeting {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 24px;
}

.quick-play-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 40px;
}

.quick-play-card {
  background-color: #181818;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 16px;
  height: 80px;
  overflow: hidden;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.quick-play-card:hover {
  background-color: #282828;
}

.quick-play-card:hover .play-btn {
  opacity: 1;
  transform: translateY(0);
}

.quick-play-card img {
  width: 80px;
  height: 80px;
  object-fit: cover;
}

.quick-play-card span {
  font-weight: 600;
  font-size: 16px;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.card {
  background-color: #181818;
  padding: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
}

.card:hover {
  background-color: #282828;
}

.card:hover .play-btn {
  opacity: 1;
  transform: translateY(0);
}

.card-image {
  width: 100%;
  aspect-ratio: 1;
  background-color: #333;
  border-radius: 4px;
  margin-bottom: 16px;
  position: relative;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-btn {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 48px;
  height: 48px;
  background-color: #1ed760;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transform: translateY(8px);
  transition: all 0.3s;
  box-shadow: 0 8px 8px rgba(0, 0, 0, 0.3);
}

.play-btn:hover {
  transform: scale(1.05) translateY(0);
  background-color: #1fdf64;
}

.play-btn svg {
  width: 20px;
  height: 20px;
  fill: #000;
  margin-left: 2px;
}

.card-title {
  font-weight: 600;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-description {
  font-size: 14px;
  color: #b3b3b3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.songs-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 40px;
}

.player {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #181818;
  border-top: 1px solid #282828;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.player-left {
  display: flex;
  align-items: center;
  gap: 14px;
  min-width: 180px;
  width: 30%;
}

.player-left img {
  width: 56px;
  height: 56px;
  border-radius: 4px;
  background-color: #333;
}

.song-info h4 {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 4px;
}

.song-info p {
  font-size: 12px;
  color: #b3b3b3;
}

.heart-icon {
  color: #b3b3b3;
  cursor: pointer;
  transition: color 0.2s;
}

.heart-icon:hover {
  color: #fff;
}

.player-center {
  flex: 1;
  max-width: 722px;
}

.player-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 8px;
}

.control-btn {
  background: none;
  border: none;
  color: #b3b3b3;
  cursor: pointer;
  transition: color 0.2s;
  padding: 0;
  display: flex;
  align-items: center;
}

.control-btn:hover {
  color: #fff;
}

.control-btn.play {
  background-color: #fff;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn.play:hover {
  transform: scale(1.06);
}

.progress-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time {
  font-size: 12px;
  color: #b3b3b3;
  min-width: 40px;
}

.progress {
  flex: 1;
  height: 4px;
  background-color: #4d4d4d;
  border-radius: 2px;
  cursor: pointer;
  position: relative;
}

.progress:hover .progress-fill::after,
.progress.dragging .progress-fill::after {
  opacity: 1;
}

.progress-fill {
  height: 100%;
  background-color: #fff;
  border-radius: 2px;
  width: 30%;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background-color: #fff;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s;
}

.player-right {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 180px;
  width: 30%;
  justify-content: flex-end;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  max-width: 125px;
}


/* Authentication styles */
.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 400;
  color: #fff;
}

.sign-out-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-top: 2px;
  text-align: left;
}

.login-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

/* Authentication Pages */
.auth-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1ed760 0%, #1db954 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.auth-card {
  background: #000;
  border-radius: 8px;
  padding: 48px;
  width: 100%;
  max-width: 450px;
  box-shadow: 0 4px 60px rgba(0, 0, 0, 0.5);
}

.auth-logo {
  text-align: center;
  margin-bottom: 48px;
}

.auth-logo h1 {
  color: #1ed760;
  font-size: 32px;
  font-weight: bold;
  margin: 0;
}

.auth-title {
  color: #fff;
  font-size: 32px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 32px;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-row .form-group {
  flex: 1;
  width: 100%;
}

.form-row .form-input {
  width: 100%;
}

.form-label {
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.form-input {
  background: #121212;
  border: 1px solid #727272;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  padding: 12px 16px;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #1ed760;
}

.form-input::placeholder {
  color: #b3b3b3;
}

.form-error {
  color: #e22134;
  font-size: 12px;
  margin-top: 4px;
}

.auth-button {
  background: #1ed760;
  border: none;
  border-radius: 50px;
  color: #000;
  font-size: 16px;
  font-weight: bold;
  padding: 16px 32px;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 16px;
}

.auth-button:hover {
  background: #1fdf64;
  transform: scale(1.04);
}

.auth-button:disabled {
  background: #535353;
  color: #b3b3b3;
  cursor: not-allowed;
  transform: none;
}

.auth-divider {
  display: flex;
  align-items: center;
  margin: 32px 0;
  color: #b3b3b3;
  font-size: 14px;
}

.auth-divider::before,
.auth-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: #727272;
}

.auth-divider::before {
  margin-right: 16px;
}

.auth-divider::after {
  margin-left: 16px;
}

.auth-link {
  text-align: center;
  margin-top: 32px;
  color: #b3b3b3;
  font-size: 14px;
}

.auth-link a {
  color: #1ed760;
  text-decoration: none;
  font-weight: 600;
}

.auth-link a:hover {
  text-decoration: underline;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #000;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .sidebar {
    width: 80px;
  }

  .nav-item span, .playlist-item, .logo {
    display: none;
  }

  .logo::first-letter {
    display: block;
  }

  .auth-card {
    padding: 32px 24px;
  }

  .auth-title {
    font-size: 28px;
  }

  .form-row {
    flex-direction: column;
    gap: 16px;
  }
}

/* Modern Login Page Styles */
.login-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: #0a0a0a;
}

.animated-bg {
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.gradient-sphere {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px);
  opacity: 0.5;
  animation: float 20s infinite ease-in-out;
}

.sphere-1 {
  width: 600px;
  height: 600px;
  background: linear-gradient(135deg, #1ed760, #1db954);
  top: -200px;
  left: -200px;
  animation-delay: 0s;
}

.sphere-2 {
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  bottom: -100px;
  right: -100px;
  animation-delay: 5s;
}

.sphere-3 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 10s;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
}

.login-content {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
}

.login-branding {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.brand-content {
  max-width: 400px;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.logo-icon {
  width: 60px;
  height: 60px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.brand-name {
  font-size: 48px;
  font-weight: bold;
  background: linear-gradient(135deg, #1ed760, #1db954);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.brand-tagline {
  font-size: 20px;
  color: #b3b3b3;
  margin-bottom: 48px;
}

.brand-features {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #fff;
  font-size: 16px;
}

.feature-icon {
  font-size: 24px;
}

.login-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form-wrapper {
  width: 100%;
  max-width: 420px;
}

.form-header {
  margin-bottom: 40px;
}

.form-header h2 {
  font-size: 32px;
  font-weight: bold;
  color: #fff;
  margin: 0 0 8px 0;
}

.form-header p {
  font-size: 16px;
  color: #b3b3b3;
  margin: 0;
}

.modern-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Name fields row - for first name and last name side by side */
.name-fields-row {
  display: flex;
  gap: 16px;
}

.name-fields-row .input-group {
  flex: 1;
  width: 100%;
}

.input-group {
  position: relative;
  transition: all 0.3s;
}

.modern-input {
  width: 100%;
  padding: 20px 48px 8px 48px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #fff;
  font-size: 16px;
  transition: all 0.3s;
}

.modern-input:focus {
  outline: none;
  border-color: #1ed760;
  background: rgba(255, 255, 255, 0.08);
}

.modern-label {
  position: absolute;
  left: 48px;
  top: 50%;
  transform: translateY(-50%);
  color: #b3b3b3;
  font-size: 16px;
  pointer-events: none;
  transition: all 0.3s;
}

.input-group.focused .modern-label,
.input-group.filled .modern-label {
  top: 12px;
  font-size: 12px;
  color: #1ed760;
}

.input-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #b3b3b3;
  transition: color 0.3s;
}

.input-group.focused .input-icon {
  color: #1ed760;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #b3b3b3;
  cursor: pointer;
  padding: 4px;
  transition: color 0.3s;
}

.password-toggle:hover {
  color: #fff;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
}

.checkbox-container input {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #b3b3b3;
  border-radius: 4px;
  transition: all 0.3s;
  position: relative;
}

.checkbox-container input:checked + .checkbox-custom {
  background: #1ed760;
  border-color: #1ed760;
}

.checkbox-container input:checked + .checkbox-custom::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid #000;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-label {
  color: #b3b3b3;
  font-size: 14px;
}

.forgot-link {
  color: #1ed760;
  font-size: 14px;
  text-decoration: none;
  transition: opacity 0.3s;
}

.forgot-link:hover {
  opacity: 0.8;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(226, 33, 52, 0.1);
  border: 1px solid rgba(226, 33, 52, 0.3);
  border-radius: 8px;
  color: #ff4458;
  font-size: 14px;
}

.submit-button {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #1ed760, #1db954);
  border: none;
  border-radius: 50px;
  color: #000;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(30, 215, 96, 0.3);
}

.submit-button:disabled {
  background: #535353;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.button-loader {
  display: flex;
  gap: 4px;
}

.loader-dot {
  width: 8px;
  height: 8px;
  background: #000;
  border-radius: 50%;
  animation: loader-bounce 1.4s infinite ease-in-out both;
}

.loader-dot:nth-child(1) { animation-delay: -0.32s; }
.loader-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loader-bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.social-divider {
  text-align: center;
  margin: 32px 0 24px;
  position: relative;
}

.social-divider span {
  background: #0a0a0a;
  padding: 0 16px;
  color: #b3b3b3;
  font-size: 14px;
  position: relative;
  z-index: 1;
}

.social-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
}

.social-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 32px;
}

.social-btn {
  flex: 1;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.social-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.signup-link {
  text-align: center;
  color: #b3b3b3;
  font-size: 14px;
}

.signup-link a {
  color: #1ed760;
  text-decoration: none;
  font-weight: 600;
  margin-left: 4px;
  transition: opacity 0.3s;
}

.signup-link a:hover {
  opacity: 0.8;
}

.shake-animation {
  animation: shake 0.5s;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-10px); }
  75% { transform: translateX(10px); }
}

/* Responsive Design */
@media (max-width: 968px) {
  .login-branding {
    display: none;
  }
  
  .login-form-container {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .login-form-wrapper {
    padding: 0 20px;
  }
  
  .social-buttons {
    flex-direction: column;
  }
  
  .form-header h2 {
    font-size: 28px;
  }
  
  .name-fields-row {
    flex-direction: column;
    gap: 16px;
  }
}

/* User Profile Styles */
.user-profile-container {
  position: relative;
}

.user-profile-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 4px 12px 4px 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-profile-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
}

.avatar-wrapper {
  position: relative;
}

.avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  user-select: none;
}

.online-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background: #22c55e;
  border: 2px solid #121212;
  border-radius: 50%;
}

.chevron-icon {
  transition: transform 0.2s ease;
  opacity: 0.6;
}

.chevron-icon.rotate {
  transform: rotate(180deg);
}

/* Dropdown Menu */
.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  min-width: 240px;
  background: #1e1e1e;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  animation: slideDown 0.2s ease;
  overflow: hidden;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.03);
}

.dropdown-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
}

.dropdown-user-info {
  flex: 1;
  min-width: 0;
}

.dropdown-user-name {
  font-size: 14px;
  font-weight: 600;
  color: white;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-user-email {
  font-size: 12px;
  color: #9ca3af;
  margin: 2px 0 0 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: #e5e7eb;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}

.dropdown-item:hover {
  background: rgba(255, 255, 255, 0.05);
  color: white;
}

.dropdown-item svg {
  opacity: 0.7;
  flex-shrink: 0;
}

.dropdown-item:hover svg {
  opacity: 1;
}

.dropdown-item-danger {
  color: #ef4444;
}

.dropdown-item-danger:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #f87171;
}

/* Login Button */
.login-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.login-button:hover {
  background: #16a34a;
  transform: scale(1.02);
}

/* Skeleton Loader */
.skeleton-loader {
  display: flex;
  align-items: center;
  gap: 8px;
}

.skeleton-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ---- Liked Songs card ---- */
:root {
  --melodix-accent: #1db954;            /* Spotify green */
  --melodix-text: #e7e7e7;
  --melodix-muted: #a1a1aa;
  --melodix-hover: rgba(255, 255, 255, 0.06);
  --melodix-focus: 0 0 0 2px rgba(29, 185, 84, 0.5);
}

.liked-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.5rem 0.6rem;
  border-radius: 10px;
  color: var(--melodix-text);
  text-decoration: none;
  cursor: pointer;
  transition: background 120ms ease, transform 120ms ease;
}

.liked-card:hover { background: var(--melodix-hover); }
.liked-card:active { transform: translateY(1px); }

.liked-card:focus-visible {
  outline: none;
  box-shadow: var(--melodix-focus);
  background: var(--melodix-hover);
}

.liked-art {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: grid;
  place-items: center;
  color: #fff; /* heart color */
  background: linear-gradient(135deg, #6d28d9 0%, #22d3ee 55%, #9333ea 100%);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.35), inset 0 -10px 20px rgba(255, 255, 255, 0.06);
  overflow: hidden;
}

/* a subtle animated sheen */
@media (prefers-reduced-motion: no-preference) {
  .liked-art::before {
    content: "";
    position: absolute;
    inset: -40%;
    background: conic-gradient(from 0deg at 50% 50%, rgba(255,255,255,0.08), transparent 60%);
    animation: liked-spin 8s linear infinite;
    pointer-events: none;
  }
}
@keyframes liked-spin { to { transform: rotate(1turn); } }

.heart-icon { width: 18px; height: 18px; opacity: 0.95; z-index: 1; }

.liked-text { display: grid; }
.liked-title {
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  font-weight: 600;
  color: var(--melodix-accent);
  letter-spacing: 0.1px;
}

.pin-icon { width: 12px; height: 12px; color: var(--melodix-accent); opacity: 0.9; }

.liked-meta {
  margin-top: 2px;
  font-size: 12.5px;
  line-height: 1.2;
  color: var(--melodix-muted);
  display: flex;
  align-items: center;
  gap: 0.35rem;
}

/* Optional: tighten spacing inside your playlists section */
.playlists { display: grid; gap: 0.25rem; }


