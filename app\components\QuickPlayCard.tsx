'use client';

import React from 'react';

interface QuickPlayCardProps {
  title: string;
  imageUrl?: string;
  color: string;
  onClick?: () => void;
}

const QuickPlayCard: React.FC<QuickPlayCardProps> = ({ 
  title, 
  imageUrl, 
  color, 
  onClick 
}) => {
  return (
    <div className="quick-play-card" onClick={onClick}>
      <img 
        src={`data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80'%3E%3Crect fill='${color}' width='80' height='80'/%3E%3C/svg%3E`} 
        alt={title}
      />
      <span>{title}</span>
      <button className="play-btn">
        <svg viewBox="0 0 24 24">
          <path d="M8 5.14v14l11-7-11-7z"/>
        </svg>
      </button>
    </div>
  );
};

export default QuickPlayCard;