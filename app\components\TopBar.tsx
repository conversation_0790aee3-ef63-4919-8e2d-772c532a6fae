'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import SearchBar from './SearchBar';

const TopBar: React.FC = () => {
  const { data: session, status } = useSession();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleSignOut = () => {
    signOut({ callbackUrl: '/auth/login' });
  };

  const getInitials = (firstName: string, lastName: string) => {
    if (!firstName) return '?';
    return firstName.charAt(0).toUpperCase() + (lastName ? lastName.charAt(0).toUpperCase() : '');
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  if (status === 'loading') {
    return (
      <div className="top-bar">
        <div className="nav-buttons">
          <button className="nav-btn">
            <svg viewBox="0 0 16 16" fill="currentColor" width="16" height="16">
              <path d="M11.03.47a.75.75 0 010 1.06L4.56 8l6.47 6.47a.75.75 0 11-1.06 1.06L2.44 8 9.97.47a.75.75 0 011.06 0z"/>
            </svg>
          </button>
          <button className="nav-btn">
            <svg viewBox="0 0 16 16" fill="currentColor" width="16" height="16">
              <path d="M4.97.47a.75.75 0 000 1.06L11.44 8l-6.47 6.47a.75.75 0 101.06 1.06L13.56 8 6.03.47a.75.75 0 00-1.06 0z"/>
            </svg>
          </button>
        </div>
        <SearchBar />
        <div className="user-profile-container">
          <div className="skeleton-loader">
            <div className="skeleton-avatar"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="top-bar">
      <div className="nav-buttons">
        <button className="nav-btn">
          <svg viewBox="0 0 16 16" fill="currentColor" width="16" height="16">
            <path d="M11.03.47a.75.75 0 010 1.06L4.56 8l6.47 6.47a.75.75 0 11-1.06 1.06L2.44 8 9.97.47a.75.75 0 011.06 0z"/>
          </svg>
        </button>
        <button className="nav-btn">
          <svg viewBox="0 0 16 16" fill="currentColor" width="16" height="16">
            <path d="M4.97.47a.75.75 0 000 1.06L11.44 8l-6.47 6.47a.75.75 0 101.06 1.06L13.56 8 6.03.47a.75.75 0 00-1.06 0z"/>
          </svg>
        </button>
      </div>

      <SearchBar />
      
      {session ? (
        <div className="user-profile-container" ref={dropdownRef}>
          <button 
            className="user-profile-button"
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            aria-expanded={isDropdownOpen}
            aria-haspopup="true"
          >
            <div className="avatar-wrapper">
              <div className="avatar">
                {getInitials(session.user.firstName, session.user.lastName)}
              </div>
              <div className="online-indicator"></div>
            </div>
            <svg 
              className={`chevron-icon ${isDropdownOpen ? 'rotate' : ''}`} 
              width="12" 
              height="12" 
              viewBox="0 0 12 12" 
              fill="currentColor"
            >
              <path d="M6 8.5L2 4.5h8L6 8.5z"/>
            </svg>
          </button>

          {isDropdownOpen && (
            <div className="dropdown-menu">
              <div className="dropdown-header">
                <div className="dropdown-avatar">
                  {getInitials(session.user.firstName, session.user.lastName)}
                </div>
                <div className="dropdown-user-info">
                  <p className="dropdown-user-name">
                    {session.user.firstName} {session.user.lastName}
                  </p>
                  <p className="dropdown-user-email">{session.user.email}</p>
                </div>
              </div>
              
              <div className="dropdown-divider"></div>
              
              <Link href="/profile" className="dropdown-item">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M8 8a3 3 0 100-6 3 3 0 000 6zm0 1c-2.67 0-8 1.34-8 4v1h16v-1c0-2.66-5.33-4-8-4z"/>
                </svg>
                <span>Your Profile</span>
              </Link>
              
              <Link href="/settings" className="dropdown-item">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M8 10.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5zM8 2c.28 0 .5.22.5.5v1.07c.76.15 1.48.46 2.09.97l.76-.76a.5.5 0 01.71.71l-.76.76c.51.61.82 1.33.97 2.09h1.23a.5.5 0 010 1h-1.23c-.15.76-.46 1.48-.97 2.09l.76.76a.5.5 0 01-.71.71l-.76-.76c-.61.51-1.33.82-2.09.97V13.5a.5.5 0 01-1 0v-1.07c-.76-.15-1.48-.46-2.09-.97l-.76.76a.5.5 0 01-.71-.71l.76-.76c-.51-.61-.82-1.33-.97-2.09H2.5a.5.5 0 010-1h1.23c.15-.76.46-1.48.97-2.09l-.76-.76a.5.5 0 01.71-.71l.76.76c.61-.51 1.33-.82 2.09-.97V2.5c0-.28.22-.5.5-.5z"/>
                </svg>
                <span>Settings</span>
              </Link>
              
              <div className="dropdown-divider"></div>
              
              <button onClick={handleSignOut} className="dropdown-item dropdown-item-danger">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                  <path d="M6 12.5a.5.5 0 00.5-.5v-1.5a.5.5 0 00-1 0V12a.5.5 0 00.5.5zM6 3.5a.5.5 0 00-.5.5v1.5a.5.5 0 001 0V4a.5.5 0 00-.5-.5zM10.5 8a.5.5 0 00-.5-.5H4a.5.5 0 000 1h6a.5.5 0 00.5-.5zM14 8A6 6 0 112 8a6 6 0 0112 0z"/>
                </svg>
                <span>Sign Out</span>
              </button>
            </div>
          )}
        </div>
      ) : (
        <div className="user-profile-container">
          <Link href="/auth/login">
            <button className="login-button">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M8 8a3 3 0 100-6 3 3 0 000 6zm0 1c-2.67 0-8 1.34-8 4v1h16v-1c0-2.66-5.33-4-8-4z"/>
              </svg>
              <span>Sign In</span>
            </button>
          </Link>
        </div>
      )}
    </div>
  );
};

export default TopBar;