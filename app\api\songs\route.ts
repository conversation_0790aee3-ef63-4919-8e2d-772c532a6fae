import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '../../lib/db';
import Song from '../../lib/models/Song';

export async function GET(request: NextRequest) {
  try {
    // Connect to database
    await connectToDatabase();

    // Get songs with pagination
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Get total count
    const totalSongs = await Song.countDocuments();

    // Get songs
    const songs = await Song.find()
      .populate('uploadedBy', 'username firstName lastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    return NextResponse.json({
      songs,
      pagination: {
        total: totalSongs,
        page,
        limit,
        totalPages: Math.ceil(totalSongs / limit),
      },
    });

  } catch (error) {
    console.error('Get songs error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch songs' },
      { status: 500 }
    );
  }
}