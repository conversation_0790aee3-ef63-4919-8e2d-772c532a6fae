'use client';

import React, { useState, useEffect, useRef } from 'react';
import { usePlayer } from '../contexts/PlayerContext';

const VolumeControl: React.FC = () => {
  const { volume, setVolume } = usePlayer();
  const [isDragging, setIsDragging] = useState(false);
  const progressRef = useRef<HTMLDivElement>(null);

  const speakerPath = "M9.741.85a.75.75 0 01.375.65v13a.75.75 0 01-1.125.65l-6.925-4a3.642 3.642 0 01-1.33-4.967 3.639 3.639 0 011.33-1.332l6.925-4a.75.75 0 01.75 0z";
  const bar1Path = "m-6.924 5.3a2.139 2.139 0 000 3.7l5.8 3.35V2.8l-5.8 3.35z";
  const bar2Path = "m8.683 4.29V5.56a2.75 2.75 0 010 4.88z";
  const bar3Path = "M11.5 13.614a5.752 5.752 0 000-11.228v1.55a4.252 4.252 0 010 8.127v1.55z";
  const lowPath = speakerPath + bar1Path;
  const mediumPath = speakerPath + bar1Path + bar2Path;
  const mutePath = "M6.717 3.55A.75.75 0 017.462 3h3.028a.75.75 0 01.743.648l.545 4.927a.75.75 0 01-1.488.135l-.51-4.59H7.462a.75.75 0 01-.745-.648l-.545-4.927a.75.75 0 011.488-.135l.51 4.59zm4.486 8.49a.75.75 0 00-1.06-1.06L8 11.06l-2.147-2.147a.75.75 0 00-1.06 1.06l2.5 2.5a.75.75 0 001.06 0l2.853-2.853z";

  const handleVolumeChange = (e: React.MouseEvent<HTMLDivElement> | MouseEvent) => {
    if (!progressRef.current) return;
    
    const progressBar = progressRef.current;
    const rect = progressBar.getBoundingClientRect();
    const clientX = e instanceof MouseEvent ? e.clientX : e.clientX;
    const clickPosition = clientX - rect.left;
    const width = progressBar.offsetWidth;
    const percentage = Math.max(0, Math.min(100, (clickPosition / width) * 100));
    setVolume(percentage / 100); // Convert to 0-1 range
  };

  const handleVolumeMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(true);
    handleVolumeChange(e);
  };

  const handleVolumeMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging) {
      handleVolumeChange(e);
    }
  };

  const handleVolumeMouseUp = () => {
    setIsDragging(false);
  };

  const toggleMute = () => {
    setVolume(volume > 0 ? 0 : 0.7);
  };

  // Handle mouse events for dragging
  useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        handleVolumeChange(e);
      }
    };

    const handleGlobalMouseUp = () => {
      if (isDragging) {
        setIsDragging(false);
      }
    };

    if (isDragging) {
      window.addEventListener('mousemove', handleGlobalMouseMove);
      window.addEventListener('mouseup', handleGlobalMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleGlobalMouseMove);
      window.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [isDragging]);

  return (
    <div className="volume-control">
      <button className="control-btn" onClick={toggleMute}>
        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
          {volume === 0 ? (
            <path d={mutePath} />
          ) : volume < 0.33 ? (
            <path d={lowPath} />
          ) : volume < 0.66 ? (
            <path d={mediumPath} />
          ) : (
            <>
              <path d={mediumPath} />
              <path d={bar3Path} />
            </>
          )}
        </svg>
      </button>
      <div
        ref={progressRef}
        className={`progress ${isDragging ? 'dragging' : ''}`}
        onClick={handleVolumeChange}
        onMouseDown={handleVolumeMouseDown}
        onMouseMove={handleVolumeMouseMove}
        onMouseUp={handleVolumeMouseUp}
      >
        <div
          className="progress-fill"
          style={{
            width: `${volume * 100}%`
          } as React.CSSProperties}
        ></div>
      </div>
    </div>
  );
};

export default VolumeControl;