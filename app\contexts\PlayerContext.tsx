'use client';

import React, { createContext, useContext, useState, useRef, useEffect } from 'react';

interface Song {
  _id: string;
  title: string;
  author: string;
  coverImageUrl: string;
  audioUrl: string;
  uploadedBy?: {
    username?: string;
    firstName?: string;
    lastName?: string;
  };
  createdAt: string;
}

interface PlayerContextType {
  currentSong: Song | null;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  play: (song: Song) => void;
  pause: () => void;
  togglePlay: () => void;
  setVolume: (volume: number) => void;
  seek: (time: number) => void;
  next: () => void;
  previous: () => void;
  playlist: Song[];
  setPlaylist: (songs: Song[]) => void;
}

const PlayerContext = createContext<PlayerContextType | undefined>(undefined);

export const usePlayer = () => {
  const context = useContext(PlayerContext);
  if (!context) {
    throw new Error('usePlayer must be used within a PlayerProvider');
  }
  return context;
};

interface PlayerProviderProps {
  children: React.ReactNode;
}

export const PlayerProvider: React.FC<PlayerProviderProps> = ({ children }) => {
  // Initialize state from localStorage on client side only
  const getInitialPlayerState = () => {
    if (typeof window !== 'undefined') {
      try {
        const savedState = localStorage.getItem('melodix-player-state');
        if (savedState) {
          const { song, time, playing, vol } = JSON.parse(savedState);
          return {
            currentSong: song,
            currentTime: time || 0,
            isPlaying: false, // Always set to false on page reload
            volume: vol || 0.7,
          };
        }
      } catch (error) {
        console.error('Error loading player state from localStorage:', error);
      }
    }
    return {
      currentSong: null,
      currentTime: 0,
      isPlaying: false,
      volume: 0.7,
    };
  };

  const [currentSong, setCurrentSong] = useState<Song | null>(getInitialPlayerState().currentSong);
  const [isPlaying, setIsPlaying] = useState(getInitialPlayerState().isPlaying);
  const [currentTime, setCurrentTime] = useState(getInitialPlayerState().currentTime);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(getInitialPlayerState().volume);
  const [playlist, setPlaylist] = useState<Song[]>([]);
  
  // Keys for local storage
  const PLAYER_STATE_KEY = 'melodix-player-state';
  
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const fadeIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  // Fade constants (in milliseconds)
  const FADE_DURATION = 250; // 200ms fade in/out (faster)
  const FADE_STEP = 0.05; // Volume change per step

  // Load player state from local storage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedState = localStorage.getItem(PLAYER_STATE_KEY);
        if (savedState) {
          const { song, time, playing, vol } = JSON.parse(savedState);
          
          // Set up the audio with saved position but don't play
          if (song) {
            // Set a small delay to ensure audioRef is set up
            setTimeout(() => {
              if (audioRef.current) {
                audioRef.current.src = song.audioUrl;
                audioRef.current.currentTime = time || 0;
                audioRef.current.volume = vol || 0.7;
              }
            }, 100);
          }
        }
      } catch (error) {
        console.error('Error loading player state from localStorage:', error);
      }
    }
  }, []);
  
  // Save player state to local storage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const stateToSave = {
          song: currentSong,
          time: currentTime,
          playing: isPlaying,
          vol: volume
        };
        localStorage.setItem(PLAYER_STATE_KEY, JSON.stringify(stateToSave));
      } catch (error) {
        console.error('Error saving player state to localStorage:', error);
      }
    }
  }, [currentSong, currentTime, isPlaying, volume]);

  // Update volume when it changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
  }, [volume]);

  // Fade in effect
  const fadeIn = () => {
    if (fadeIntervalRef.current) {
      clearInterval(fadeIntervalRef.current);
    }
    
    let currentVolume = 0;
    const targetVolume = volume;
    const startTime = Date.now();
    
    fadeIntervalRef.current = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / FADE_DURATION, 1);
      
      // Calculate current volume with easing function for smoother transition
      currentVolume = targetVolume * progress;
      
      if (audioRef.current) {
        audioRef.current.volume = currentVolume;
      }
      
      if (progress >= 1) {
        if (fadeIntervalRef.current) {
          clearInterval(fadeIntervalRef.current);
          fadeIntervalRef.current = null;
        }
      }
    }, FADE_STEP * 20); // Update every 20ms for smooth transition
  };

  // Fade out effect
  const fadeOut = (callback?: () => void) => {
    if (fadeIntervalRef.current) {
      clearInterval(fadeIntervalRef.current);
    }
    
    const startVolume = audioRef.current ? audioRef.current.volume : 0;
    const startTime = Date.now();
    
    fadeIntervalRef.current = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / FADE_DURATION, 1);
      
      // Calculate current volume with easing function
      const currentVolume = startVolume * (1 - progress);
      
      if (audioRef.current) {
        audioRef.current.volume = currentVolume;
      }
      
      if (progress >= 1) {
        if (fadeIntervalRef.current) {
          clearInterval(fadeIntervalRef.current);
          fadeIntervalRef.current = null;
        }
        
        // Execute callback after fade is complete
        if (callback) {
          callback();
        }
      }
    }, FADE_STEP * 20); // Update every 20ms for smooth transition
  };


  const play = (song: Song) => {
    if (audioRef.current) {
      audioRef.current.src = song.audioUrl;
      
      // Check if we're resuming the same song and restore position
      if (currentSong && currentSong._id === song._id && currentTime > 0) {
        audioRef.current.currentTime = currentTime;
      } else {
        // Reset position if it's a different song
        audioRef.current.currentTime = 0;
      }
      
      // Set initial volume to 0 for fade in effect
      audioRef.current.volume = 0;
      
      audioRef.current.play();
      setCurrentSong(song);
      setIsPlaying(true);
      
      // Start fade in effect
      fadeIn();
    }
  };

  const pause = () => {
    if (audioRef.current) {
      // Fade out before pausing
      fadeOut(() => {
        audioRef.current?.pause();
        setIsPlaying(false);
      });
    }
  };

  const togglePlay = () => {
    if (!currentSong) return;
    
    if (isPlaying) {
      pause();
    } else {
      if (audioRef.current) {
        audioRef.current.src = currentSong.audioUrl;
        
        // Restore the saved playback position when resuming
        if (currentTime > 0) {
          audioRef.current.currentTime = currentTime;
        }
        
        // Set initial volume to 0 for fade in effect
        audioRef.current.volume = 0;
        
        audioRef.current.play();
        setIsPlaying(true);
        
        // Start fade in effect
        fadeIn();
      }
    }
  };

  // Initialize audio element
  useEffect(() => {
    if (typeof window !== 'undefined') {
      audioRef.current = new Audio();
      
      const handleTimeUpdate = () => {
        if (audioRef.current) {
          setCurrentTime(audioRef.current.currentTime);
        }
      };
      
      const handleLoadedMetadata = () => {
        if (audioRef.current) {
          setDuration(audioRef.current.duration);
        }
      };
      
      const handleEnded = () => {
        setIsPlaying(false);
        // Auto-play next song if available
        if (playlist.length > 0) {
          const currentIndex = currentSong ? playlist.findIndex(song => song._id === currentSong._id) : -1;
          if (currentIndex < playlist.length - 1) {
            play(playlist[currentIndex + 1]);
          }
        }
      };
      
      const handleError = (e: ErrorEvent) => {
        console.error('Audio error:', e);
        setIsPlaying(false);
      };
      
      if (audioRef.current) {
        audioRef.current.addEventListener('timeupdate', handleTimeUpdate);
        audioRef.current.addEventListener('loadedmetadata', handleLoadedMetadata);
        audioRef.current.addEventListener('ended', handleEnded);
        audioRef.current.addEventListener('error', handleError);
        audioRef.current.volume = volume;
      }
      
      return () => {
        if (audioRef.current) {
          audioRef.current.removeEventListener('timeupdate', handleTimeUpdate);
          audioRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata);
          audioRef.current.removeEventListener('ended', handleEnded);
          audioRef.current.removeEventListener('error', handleError);
          audioRef.current.pause();
          audioRef.current.src = '';
        }
        
        // Clear any ongoing fade effects
        if (fadeIntervalRef.current) {
          clearInterval(fadeIntervalRef.current);
          fadeIntervalRef.current = null;
        }
      };
    }
  }, [play, playlist, currentSong, volume]);

  // Add keyboard event listener for space bar
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Check if space bar is pressed (keyCode 32 or ' ')
      if (e.code === 'Space' || e.keyCode === 32) {
        // Prevent default behavior (scrolling) when space is pressed
        e.preventDefault();
        togglePlay();
      }
    };

    // Add event listener
    window.addEventListener('keydown', handleKeyDown);

    // Clean up event listener on unmount
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [togglePlay]);

  const seek = (time: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const next = () => {
    if (!currentSong || playlist.length === 0) return;
    
    const currentIndex = playlist.findIndex(song => song._id === currentSong._id);
    if (currentIndex < playlist.length - 1) {
      // Fade out before switching to next song
      fadeOut(() => {
        play(playlist[currentIndex + 1]);
      });
    }
  };

  const previous = () => {
    if (!currentSong || playlist.length === 0) return;
    
    const currentIndex = playlist.findIndex(song => song._id === currentSong._id);
    if (currentIndex > 0) {
      // Fade out before switching to previous song
      fadeOut(() => {
        play(playlist[currentIndex - 1]);
      });
    } else {
      // If at first song, restart it with fade in
      if (audioRef.current) {
        audioRef.current.currentTime = 0;
        audioRef.current.volume = 0; // Reset volume for fade in
        audioRef.current.play();
        setIsPlaying(true);
        fadeIn(); // Apply fade in effect
      }
    }
  };

  const value: PlayerContextType = {
    currentSong,
    isPlaying,
    currentTime,
    duration,
    volume,
    play,
    pause,
    togglePlay,
    setVolume,
    seek,
    next,
    previous,
    playlist,
    setPlaylist,
  };

  return (
    <PlayerContext.Provider value={value}>
      {children}
    </PlayerContext.Provider>
  );
};