import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      firstName: string;
      lastName?: string;
      username: string;
      email: string;
    };
  }

  interface User {
    id: string;
    firstName: string;
    lastName?: string;
    username: string;
    email: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    firstName: string;
    lastName?: string;
    username: string;
  }
}
